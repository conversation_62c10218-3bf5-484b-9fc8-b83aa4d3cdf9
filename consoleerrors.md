ar:1 
            
            
           GET http://localhost:3001/ar 500 (Internal Server Error)
react-server-dom-webpack-client.browser.development.js:1849 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
redirect-boundary.js:57 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
not-found-boundary.js:37 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
redirect-boundary.js:57 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
not-found-boundary.js:37 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
react-server-dom-webpack-client.browser.development.js:1849 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
redirect-boundary.js:57 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
not-found-boundary.js:37 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
redirect-boundary.js:57 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
not-found-boundary.js:37 Uncaught Error: Couldn't find next-intl config file. Please follow the instructions at https://next-intl-docs.vercel.app/docs/getting-started/app-router
    at getConfig (webpack-internal:///(rsc)/./node_modules/next-intl/dist/development/config.js:6:9)
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:407)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at eval (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getConfig.js:11:1047)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:352706
    at r (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useConfig.js:8:476)
    at o (webpack-internal:///(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js:7:160)
    at HomePage (webpack-internal:///(rsc)/./app/[locale]/page.tsx:11:68)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264092)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274676)
    at ej (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:264920)
    at e_ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:263962)
    at e (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268224)
    at eF (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:268712)
    at C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265943
    at Array.toJSON (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:266407)
    at stringify (<anonymous>)
    at eq (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:274775)
    at eJ (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:275293)
    at Timeout._onTimeout (C:\Users\<USER>\Documents\augment-projects\syrian-defense-ministry\node_modules\next\dist\compiled\next-server\app-page.runtime.dev.js:35:265080)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)
resolveErrorDev @ react-server-dom-webpack-client.browser.development.js:1849
processFullRow @ react-server-dom-webpack-client.browser.development.js:1922
processBinaryChunk @ react-server-dom-webpack-client.browser.development.js:2072
progress @ react-server-dom-webpack-client.browser.development.js:2153
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
progress @ react-server-dom-webpack-client.browser.development.js:2154
Promise.then
startReadingFromStream @ react-server-dom-webpack-client.browser.development.js:2161
createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2166
eval @ app-index.js:108
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js:244
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
fn @ webpack.js:371
eval @ app-next-dev.js:9
eval @ app-bootstrap.js:57
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:8
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js:266
options.factory @ webpack.js:715
__webpack_require__ @ webpack.js:37
__webpack_exec__ @ main-app.js:1985
(anonymous) @ main-app.js:1986
webpackJsonpCallback @ webpack.js:1387
(anonymous) @ webpack.js:1401
(anonymous) @ webpack.js:1403
(anonymous) @ webpack.js:1409
app-index.js:33 The above error occurred in the <NotFoundErrorBoundary> component:

    at InnerLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:243:11)
    at RedirectErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:74:9)
    at RedirectBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:76:9)
    at NotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:84:11)
    at LoadingBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:349:11)
    at ErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:160:11)
    at InnerScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:153:9)
    at ScrollAndFocusHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:228:11)
    at RenderFromTemplateContext (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js:16:44)
    at OuterLayoutRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js:370:11)
    at body
    at html
    at RootLayout (Server)
    at RedirectErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:74:9)
    at RedirectBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js:82:11)
    at NotFoundErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:76:9)
    at NotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js:84:11)
    at DevRootNotFoundBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-not-found-boundary.js:33:11)
    at ReactDevOverlay (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/ReactDevOverlay.js:87:9)
    at HotReload (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js:321:11)
    at Router (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js:207:11)
    at ErrorBoundaryHandler (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:113:9)
    at ErrorBoundary (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js:160:11)
    at AppRouter (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js:585:13)
    at ServerRoot (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js:112:27)
    at Root (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js:117:11)
    at ReactDevOverlay (webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/ReactDevOverlay.js:87:9)

React will try to recreate this component tree from scratch using the error boundary you provided, ReactDevOverlay.
window.console.error @ app-index.js:33
logCapturedError @ react-dom.development.js:15295
callback @ react-dom.development.js:15357
callCallback @ react-dom.development.js:8696
commitCallbacks @ react-dom.development.js:8743
commitClassCallbacks @ react-dom.development.js:21323
commitLayoutEffectOnFiber @ react-dom.development.js:21425
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21418
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21407
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21577
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21418
recursivelyTraverseLayoutEffects @ react-dom.development.js:22926
commitLayoutEffectOnFiber @ react-dom.development.js:21437
commitLayoutEffects @ react-dom.development.js:22912
commitRootImpl @ react-dom.development.js:26226
commitRoot @ react-dom.development.js:26077
commitRootWhenReady @ react-dom.development.js:24749
finishConcurrentRender @ react-dom.development.js:24714
performConcurrentWorkOnRoot @ react-dom.development.js:24559
workLoop @ scheduler.development.js:256
flushWork @ scheduler.development.js:225
performWorkUntilDeadline @ scheduler.development.js:534
GenAIWebpageEligibilityService.js:18 
            
            
           GET https://raw.githubusercontent.com/Bon-Appetit/porn-domains/refs/heads/master/block.txt 404 (Not Found)
fetchExplicitBlockList @ GenAIWebpageEligibilityService.js:18
getExplicitBlockList @ GenAIWebpageEligibilityService.js:18
await in getExplicitBlockList
_shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
await in _shouldShowTouchpoints
shouldShowTouchpoints @ GenAIWebpageEligibilityService.js:18
isEligible @ ActionableCoachmark.js:18
getRenderPrompt @ ShowOneChild.js:18
await in getRenderPrompt
render @ ShowOneChild.js:18
(anonymous) @ ch-content-script-dend.js:18
await in (anonymous)
(anonymous) @ ch-content-script-dend.js:18
j @ jquery-3.1.1.min.js:2
k @ jquery-3.1.1.min.js:2
setTimeout
(anonymous) @ jquery-3.1.1.min.js:2
i @ jquery-3.1.1.min.js:2
add @ jquery-3.1.1.min.js:2
(anonymous) @ jquery-3.1.1.min.js:2
Deferred @ jquery-3.1.1.min.js:2
then @ jquery-3.1.1.min.js:2
r.fn.ready @ jquery-3.1.1.min.js:2
(anonymous) @ ch-content-script-dend.js:18
ar:1 The resource http://localhost:3001/_next/static/css/app/%5Blocale%5D/layout.css?v=1750010911067 was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally.
